<template>
  <view class="navigation-container">
	  <!-- 更新 -->
	  <view class="index">
	  		<wu-app-update></wu-app-update>
	  	</view>
    <!-- 背景图片 -->
    <image class="bg-image" src="/static/<EMAIL>" mode="aspectFill"></image>
    
    <!-- 隐藏热区 -->
    <view class="secret-hotspot" @click="handleSecretHotspotClick"></view>
    
    <!-- 粒子效果和流星雨 -->
    <view class="particles-container">
      <view class="particle" v-for="(item, index) in particles" :key="`p-${index}`"
        :style="{ left: item.left, top: item.top, width: item.size, height: item.size, 
                 animationDelay: item.delay, opacity: item.opacity }">
      </view>
    </view>
    
    <view class="meteor-shower">
      <view class="meteor" v-for="(item, index) in meteors" :key="`m-${index}`"
        :style="{ left: item.left, top: item.top, width: item.width, height: item.height, 
                 animationDelay: item.delay, animationDuration: item.duration }">
      </view>
    </view>
    
    <!-- 左侧LOGO和卡通人物 -->
    <view class="left-section">
      <view class="logo-container">
        <image class="logo-image" src="/static/<EMAIL>" mode="aspectFit" @click="handleLogoClick"></image>
      </view>
      
      <image class="people-image" src="/static/<EMAIL>" mode="aspectFit"></image>
    </view>
    
    <!-- 网络错误提示 -->
    <view class="network-error-container" v-if="!networkAvailable">
      <view class="network-error-content">
        <view class="network-error-icon">
          <view class="wifi-icon">
            <image
              src="/static/no.png"
              mode="scaleToFill"
            />
          </view>
        </view>
        <text class="network-error-title">网络连接失败</text>
        <text class="network-error-desc">请检查您的网络连接，连接到WiFi或移动网络后才能继续使用应用</text>
        <view class="network-actions">
          <view class="network-btn network-btn-primary" @click="openWifiSettings">
            <text>打开网络设置</text>
          </view>
          <view class="network-btn network-btn-secondary" @click="retryConnection">
            <text>重试连接</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 右侧卡片列表 -->
    <view class="right-section" v-if="networkAvailable">
      <view class="card-container">
        <block v-if="cardList && cardList.length > 0">
          <!-- 使用for循环渲染卡片 -->
          <view class="card-group" v-for="(item, index) in cardList" :key="index">
            <block v-if="item && item.enabled">
              <view class="title-bar" :style="item.titleBgSrc ? { backgroundImage: 'url(' + item.titleBgSrc + ')' } : {}">
                <text class="title-text">{{item.title}}</text>
                <view class="indicator-container">
                  <view class="indicator indicator-1"></view>
                  <view class="indicator indicator-2"></view>
                  <view class="indicator indicator-3"></view>
                  <view class="indicator indicator-4"></view>
                </view>
              </view>
              <view class="card" 
                    :class="[item.cardClass, {'card-rotating': rotatingCards[item.type]}]"
                    @click="handleCardClick(item.type)" 
                    hover-class="card-hover">
                <view class="card-content">
                  <image class="card-icon" :src="item.iconSrc" mode="aspectFit"></image>
                </view>
              </view>
            </block>
          </view>
        </block>
        <block v-else>
          <!-- 加载中显示 -->
          <view class="loading-container">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <view class="footer-content">
        <text class="footer-text">© 2025 山东普古物联网科技有限公司 v{{appVersion}} [{{deviceModel}}]</text>
      </view>
    </view>
    
    <!-- 应用未安装弹窗 -->
    <view class="custom-popup" v-if="showPopup">
      <view class="popup-mask" @click="closePopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">{{popupTitle}}</text>
          <view class="popup-close" @click="closePopup">×</view>
        </view>
        <view class="popup-body">
          <view class="popup-icon-container">
            <view class="popup-icon-wrapper" :class="popupIconClass">
              <text class="popup-icon-text" v-if="popupType === 'warning'">!</text>
              <text class="popup-icon-text" v-else-if="popupType === 'error'">×</text>
              <text class="popup-icon-text" v-else>i</text>
            </view>
          </view>
          <text class="popup-message">{{popupMessage}}</text>
        </view>
        <view class="popup-footer">
          <view class="popup-btn popup-btn-primary" @click="closePopup">
            <text>确定</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 密码输入弹窗 -->
    <view class="password-popup" v-if="showPasswordPopup">
      <view class="popup-mask" @click="closePasswordPopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">系统模式</text>
          <view class="popup-close" @click="closePasswordPopup">×</view>
        </view>
        <view class="popup-body">
          <view class="password-input-container">
            <input type="password" v-model="passwordInput" placeholder="请输入密码" class="password-input" />
          </view>
        </view>
        <view class="popup-footer">
          <view class="popup-btn popup-btn-primary" @click="validatePassword">
            <text>确定</text>
          </view>
          <view class="popup-btn popup-btn-secondary" @click="closePasswordPopup">
            <text>取消</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 应用列表入口按钮 -->
    <!-- <view class="app-list-button" @click="navigateToAppList">
      <image class="app-list-icon" src="/static/app-list.png" mode="aspectFit"></image>
      <text class="app-list-text">应用列表</text>
    </view> -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      appVersion: '1.1.9',
      floatingItems: [],
      particles: [],
      meteors: [],
      showPopup: false,
      popupTitle: '',
      popupMessage: '',
      popupType: 'warning', // warning, error, info
      // 卡片旋转状态
      rotatingCards: {
        ai: false,
        market: false,
        shop: false
      },
      // 卡片数据列表
      cardList: [],
      // 接口URL
      apiUrl: 'https://bxcs.boxuehao.cn/app/studyList.json',
      // 网络状态
      networkAvailable: true,
      // 重试计时器
      retryTimer: null,
      // 设备信息
      deviceModel: '',
      isP16Device: false,
      // 隐藏后门
      secretHomeStatus: 'hidden', // 默认假设Home键是显示的，首次点击会隐藏
      hotspotClickTimes: [], // 热区点击时间记录
      // Logo点击暗门
      logoClickTimes: [], // logo点击时间记录
      showPasswordPopup: false, // 是否显示密码输入弹窗
      passwordInput: '', // 密码输入值
    }
  },
  computed: {
    popupIconClass() {
      return `popup-icon-${this.popupType}`;
    }
  },
  onLoad() {
    // 生成随机粒子
    this.generateParticles();
    // 生成流星雨
    this.generateMeteors();
    // 获取初始网络状态
    this.checkNetworkStatus();
    // 获取设备型号信息
    this.getDeviceInfo();
  },
  onShow() {
    // 每次回到主屏幕时隐藏Recent按钮
    this.hideRecentButton();
    
    // 隐藏Home按钮
    // this.hideHomeButton();
    
    // 设置本应用为主桌面
    this.setAppAsHomeLauncher();
    
    // 获取卡片数据前先检查网络状态
    this.checkNetworkStatus();
    // 仅在网络可用时获取卡片数据
    if(this.networkAvailable) {
      this.fetchCardData();
    }
  },
  // 处理返回按钮事件
  onBackPress(options) {
    // 当触发物理返回键或手势返回时
    if (options.from === 'backbutton') {
      console.log('拦截返回按钮事件');
      // 返回true表示阻止默认返回行为
      return true;
    } else if (options.from === 'navigateBack') {
      // 如果是通过uni.navigateBack触发的，可以允许返回
      return false;
    }
    return false;
  },
  mounted() {
    // 在页面加载时隐藏导航栏返回按钮（针对H5平台）
    // #ifdef H5
    var backbutton = document.getElementsByClassName('uni-page-head-hd')[0];
    if (backbutton) backbutton.style.display = 'none';
    // #endif
    
    // 在APP平台设置页面样式，禁用侧滑返回
    // #ifdef APP-PLUS
    const currentWebview = plus.webview.currentWebview();
    if (currentWebview) {
      currentWebview.setStyle({
        popGesture: 'none' // 禁用侧滑返回
      });
    }
    // #endif

    // 监听网络状态变化事件
    uni.$on('network-status-change', this.handleNetworkChange);
  },
  beforeDestroy() {
    // 注销网络状态变化监听，清除定时器
    uni.$off('network-status-change', this.handleNetworkChange);
    if(this.retryTimer) {
      clearInterval(this.retryTimer);
    }
  },
  methods: {
    // 处理Logo点击事件
    handleLogoClick() {
      // 记录点击时间
      const now = Date.now();
      
      // 如果3秒内点击了10次，弹出密码输入框
      if (!this.logoClickTimes) {
        this.logoClickTimes = [];
      }
      
      // 添加当前点击时间
      this.logoClickTimes.push(now);
      
      // 只保留最近3秒内的点击记录
      const recentTime = now - 3000;
      this.logoClickTimes = this.logoClickTimes.filter(time => time > recentTime);
      
      // 如果短时间内连续点击10次以上，触发密码输入框
      if (this.logoClickTimes.length >= 10) {
        this.showPasswordPopup = true;
        // 清空点击记录
        this.logoClickTimes = [];
        
        // 提供微妙的振动反馈
        // #ifdef APP-PLUS
        uni.vibrateShort({
          success: function() {
            console.log('振动成功');
          }
        });
        // #endif
      }
    },
    
    // 关闭密码输入弹窗
    closePasswordPopup() {
      this.showPasswordPopup = false;
      this.passwordInput = '';
    },
    
    // 验证密码
    validatePassword() {
      if (this.passwordInput === 'bxcs521') {
        // 密码正确，先发送显示系统按钮的广播，再返回原始桌面
        this.showSystemButtons();
        
        // 发送设置系统原始桌面为默认桌面的广播
        // #ifdef APP-PLUS
        if (plus.os.name.toLowerCase() === 'android') {
          try {
            const main = plus.android.runtimeMainActivity();
            const Intent = plus.android.importClass('android.content.Intent');
            
            // 发送设置默认桌面的广播
            const intentSetLauncher = new Intent('com.hra.setHomeLauncher');
            intentSetLauncher.putExtra('PkgName', 'com.android.launcher3');
            intentSetLauncher.putExtra('ClassName', 'com.android.launcher3.uioverrides.QuickstepLauncher');
            main.sendBroadcast(intentSetLauncher);
            console.log('已发送设置原始桌面为默认桌面的广播');
          } catch (e) {
            console.error('发送设置默认桌面广播失败:', e);
          }
        }
        // #endif
        
        this.returnToOriginalLauncher();
        this.closePasswordPopup();
      } else {
        // 密码错误
        this.showCustomPopup('错误', '密码不正确', 'error');
        this.passwordInput = '';
      }
    },
    
    // 显示系统按钮（Home和Recent）
    showSystemButtons() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity();
          const Intent = plus.android.importClass('android.content.Intent');
          
          // 发送显示Recent按钮的广播
          const intentShowRecent = new Intent('com.hra.showRecent');
          main.sendBroadcast(intentShowRecent);
          console.log('已发送Recent按钮显示广播');
          
          // 发送显示Home按钮的广播
          const intentShowHome = new Intent('com.hra.showHome');
          main.sendBroadcast(intentShowHome);
          console.log('已发送Home按钮显示广播');
          
          // 更新状态
          this.secretHomeStatus = 'shown';
          
          // 提供微妙的成功反馈
          uni.vibrateLong({
            success: function() {
              console.log('振动成功');
            }
          });
        } catch (e) {
          console.error('发送广播失败:', e);
        }
      }
      // #endif
    },
    
    // 返回原始桌面的方法
    returnToOriginalLauncher() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const Intent = plus.android.importClass("android.content.Intent");
          const ComponentName = plus.android.importClass("android.content.ComponentName");
          
          const mainActivity = plus.android.runtimeMainActivity();
          const mLaunchIntent = new Intent();
          const cn = new ComponentName("com.android.launcher3", "com.android.launcher3.uioverrides.QuickstepLauncher");
          
          mLaunchIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
          mLaunchIntent.setComponent(cn);
          
          mainActivity.startActivity(mLaunchIntent);
          
          console.log('已返回原始桌面');
        } catch (e) {
          console.error('返回原始桌面失败:', e);
          this.showCustomPopup('操作失败', '无法返回原始桌面，请联系管理员。', 'error');
        }
      }
      // #endif
      
      // #ifndef APP-PLUS
      this.showCustomPopup('提示', '此功能仅在APP环境下可用', 'info');
      // #endif
    },
    
    // 隐藏Recent按钮
    hideRecentButton() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity();
          const Intent = plus.android.importClass('android.content.Intent');
          
          // 发送隐藏Recent按钮的广播
          const intent = new Intent('com.hra.hideRecent');
          main.sendBroadcast(intent);
          console.log('已发送Recent按钮隐藏广播');
        } catch (e) {
          console.error('发送隐藏Recent按钮广播失败:', e);
        }
      }
      // #endif
    },
    
    // 检查网络状态
    checkNetworkStatus() {
      // 尝试从App全局数据获取网络状态
      const app = getApp();
      if(app && app.globalData) {
        this.networkAvailable = app.globalData.networkAvailable;
      } else {
        // 手动获取网络状态
        uni.getNetworkType({
          success: (res) => {
            console.log('获取当前网络类型:', res.networkType);
            this.networkAvailable = res.networkType !== 'none';
          }
        });
      }
    },
    
    // 处理网络状态变化
    handleNetworkChange(res) {
      console.log('收到网络状态变化事件:', res);
      this.networkAvailable = res.available;
      
      if(res.available) {
        // 网络恢复，重新获取数据
        console.log('网络已恢复，重新获取数据');
        this.fetchCardData();
        // 停止自动重试
        if(this.retryTimer) {
          clearInterval(this.retryTimer);
          this.retryTimer = null;
        }
      } else {
        console.log('网络已断开');
        // 清空卡片数据
        // this.cardList = [];
      }
    },
    
    // 打开WiFi设置
    openWifiSettings() {
      // #ifdef APP-PLUS
      if(plus.os.name.toLowerCase() === 'android') {
        // Android平台
        try {
          const Intent = plus.android.importClass("android.content.Intent");
          const Settings = plus.android.importClass("android.provider.Settings");
          const mainActivity = plus.android.runtimeMainActivity();
          const wifiSettingsIntent = new Intent(Settings.ACTION_WIFI_SETTINGS);
          mainActivity.startActivity(wifiSettingsIntent);
        } catch(e) {
          console.error('打开WiFi设置失败:', e);
          this.showCustomPopup('操作失败', '无法打开WiFi设置，请手动前往系统设置开启网络。', 'error');
        }
      } else if(plus.os.name.toLowerCase() === 'ios') {
        // iOS平台，尝试打开设置页面
        try {
          plus.runtime.openURL('App-Prefs:root=WIFI');
        } catch(e) {
          console.error('打开WiFi设置失败:', e);
          this.showCustomPopup('操作失败', '无法打开WiFi设置，请手动前往系统设置开启网络。', 'error');
        }
      }
      // #endif
      
      // #ifndef APP-PLUS
      this.showCustomPopup('提示', '请手动开启网络连接后再使用应用。', 'info');
      // #endif
    },
    
    // 手动重试连接
    retryConnection() {
      this.checkNetworkStatus();
      if(this.networkAvailable) {
        this.fetchCardData();
      } else {
        // 定时检查网络状态
        if(!this.retryTimer) {
          this.retryTimer = setInterval(() => {
            this.checkNetworkStatus();
            if(this.networkAvailable) {
              this.fetchCardData();
              clearInterval(this.retryTimer);
              this.retryTimer = null;
            }
          }, 5000); // 每5秒检查一次
        }
        
        this.showCustomPopup('网络未连接', '请连接到WiFi或移动网络后使用应用。', 'warning');
      }
    },
    
    // 获取卡片数据
    fetchCardData() {
      // 如果网络不可用，显示提示并返回
      if(!this.networkAvailable) {
        console.log('网络不可用，无法获取卡片数据');
        return;
      }
      
      // 判断当前环境
      let apiPath;
      // #ifdef H5
      // H5环境下使用代理路径避免跨域
      apiPath = '/api/studyList.json'; 
      // #endif

      // #ifndef H5
      // 非H5环境下使用完整URL
      apiPath = this.apiUrl;
      // #endif
      
      uni.request({
        url: apiPath,
        success: (res) => {
          console.log('获取卡片数据成功:', res.data);
          if (res.data && res.data.cardList && Array.isArray(res.data.cardList) && res.data.cardList.length > 0) {
            // 设置卡片数据
            this.cardList = res.data.cardList;
            
            // 检查是否需要根据设备类型修改包名
            const needDeviceCheck = res.data.needDeviceCheck === true;
            console.log('接口中设备检测标志:', needDeviceCheck);
            
            // 保存到全局状态，以便其他地方也能使用
            const app = getApp();
            if (app && app.globalData) {
              app.globalData.needDeviceCheck = needDeviceCheck;
              console.log('已将设备检测标志保存到全局:', needDeviceCheck);
            }
            
            // 只有在需要检测设备时才执行
            if (needDeviceCheck && this.isP16Device) {
              this.cardList.forEach(card => {
                // 如果有needName字段且不为空，则使用needName作为包名
                if (card && card.needName && card.needName.trim() !== '') {
                  console.log(`为卡片 ${card.type} 设置特殊包名: ${card.packageName} -> ${card.needName}`);
                  card.packageName = card.needName;
                } else {
                  console.log(`卡片 ${card.type} 没有有效的needName，保持原始包名: ${card.packageName}`);
                }
              });
            } else {
              console.log('根据接口配置，不进行设备特殊检测，使用默认包名');
            }
            
            // 初始化旋转状态
            const rotatingCards = {};
            this.cardList.forEach(card => {
              if (card && card.type) {
                rotatingCards[card.type] = false;
              }
            });
            this.rotatingCards = rotatingCards;
          } else {
            console.error('卡片数据格式不正确');
            // 加载默认数据
            this.loadDefaultCardData();
          }
        },
        fail: (err) => {
          console.error('获取卡片数据失败:', err);
          // 检查是否是网络问题
          if(err.errMsg && err.errMsg.includes('fail') && 
             (err.errMsg.includes('Unable to resolve host') || 
              err.errMsg.includes('timeout') || 
              err.errMsg.includes('abort'))) {
            // 网络问题，更新网络状态
            this.networkAvailable = false;
            // 通知App全局状态
            const app = getApp();
            if(app && app.globalData) {
              app.globalData.networkAvailable = false;
            }
          }
          // 加载默认数据
          this.loadDefaultCardData();
        }
      });
    },
    
    // 加载默认卡片数据（备用）
    loadDefaultCardData() {
      this.cardList = [
        {
          type: "ai",
          title: "勃学AI督学机",
          iconSrc: "https://bxcs.boxuehao.cn/app/studyImg/<EMAIL>",
          cardClass: "ai-card",
          packageName: "com.selfstudy.boxue",
          needName: "com.zhuoxu.selfstudy", // 特殊设备使用的包名
          appName: "AI督学机",
          enabled: true,
          titleBgSrc: "https://bxcs.boxuehao.cn/app/studyImg/title1.png",
          needDeviceCheck: true // 默认开启设备检测
        },
        {
          type: "market",
          title: "融合的互动平台",
          iconSrc: "https://bxcs.boxuehao.cn/app/studyImg/<EMAIL>",
          cardClass: "market-card",
          packageName: "bscs.boran.com",
          needName: "", // 无特殊包名
          appName: "勃学超市",
          enabled: true,
          isComingSoon: true,
          titleBgSrc: "https://bxcs.boxuehao.cn/app/studyImg/title2.png",
          needDeviceCheck: false // 不需要设备检测
        },
        {
          type: "shop",
          title: "下载更新应用",
          iconSrc: "https://bxcs.boxuehao.cn/app/studyImg/<EMAIL>",
          cardClass: "shop-card",
          packageName: "",
          needName: "", // 无特殊包名
          appName: "应用市场",
          enabled: true,
          isComingSoon: true,
          titleBgSrc: "https://bxcs.boxuehao.cn/app/studyImg/title3.png",
          needDeviceCheck: false // 不需要设备检测
        }
      ];
      
      // 初始化旋转状态
      const rotatingCards = {};
      this.cardList.forEach(card => {
        if (card && card.type) {
          rotatingCards[card.type] = false;
        }
      });
      this.rotatingCards = rotatingCards;
    },
    
    // 处理卡片点击
    handleCardClick(type) {
      // 获取当前卡片数据
      const card = this.cardList.find(item => item.type === type);
      if (!card) return;
      
      // 如果卡片已经在旋转，不重复触发
      if (this.rotatingCards[type]) return;
      
      // 如果是"敬请期待"的卡片，显示提示信息
      if (card.isComingSoon) {
        this.showCustomPopup('敬请期待', '应用即将上新，敬请期待！', 'info');
        return;
      }
      
      // 如果卡片被禁用，不执行操作
      if (!card.enabled) return;
      
      // 设置旋转状态
      this.rotatingCards[type] = true;
      
      // 旋转完成后执行功能（约1秒后）
      setTimeout(() => {
        this.rotatingCards[type] = false;
        
        // 根据类型打开相应功能
        if (card.packageName) {
          this.openExternalApp(card.packageName, card.appName, type);
        }
      }, 1500);
    },
    
    // 生成粒子效果
    generateParticles() {
      const particles = [];
      
      for (let i = 0; i < 50; i++) {
        particles.push({
          left: Math.random() * 100 + 'vw',
          top: Math.random() * 100 + 'vh',
          size: (Math.random() * 6 + 1) + 'px',
          delay: (Math.random() * 5) + 's',
          opacity: Math.random() * 0.5 + 0.1
        });
      }
      
      this.particles = particles;
    },
    
    // 生成流星雨
    generateMeteors() {
      const meteors = [];
      
      for (let i = 0; i < 8; i++) {
        meteors.push({
          left: (50 + Math.random() * 40) + 'vw',
          top: Math.random() * 20 + 'vh',
          width: (Math.random() * 200 + 100) + 'px',
          height: '1px',
          delay: (Math.random() * 15) + 's',
          duration: (Math.random() * 3 + 1) + 's'
        });
      }
      
      this.meteors = meteors;
    },
    
    // 检查应用是否已安装
    checkAppInstalled(packageName) {
      let isInstalled = false;
      
      // 仅在App环境下执行
      // #ifdef APP-PLUS
      if(plus.os.name.toLowerCase() === 'android') {
        isInstalled = plus.runtime.isApplicationExist({pname: packageName});
        console.log(`检查应用[${packageName}]是否安装: ${isInstalled}`);
      }
      // #endif
      
      return isInstalled;
    },
    
    // 打开外部应用
    openExternalApp(packageName, appName, iconType) {
      console.log(`尝试打开应用: ${appName}, 包名: ${packageName}`);
      
      // 查找当前卡片
      const card = this.cardList.find(item => item.type === iconType);
      
      // 检查是否需要根据设备类型处理包名
      const needDeviceCheck = getApp()?.globalData?.needDeviceCheck === true || false;
      
      // 如果需要检测设备类型，且设备是P16，且有needName，检查并更新包名
      if (needDeviceCheck && this.isP16Device && card && card.needName && card.needName.trim() !== '') {
        if (packageName !== card.needName) {
          console.log(`检测到P16设备，将包名从 ${packageName} 修改为 ${card.needName}`);
          packageName = card.needName;
        }
      } else if (!needDeviceCheck) {
        console.log('根据全局配置，不进行设备特殊检测，使用默认包名:', packageName);
      } else if (!card || !card.needName || card.needName.trim() === '') {
        console.log('卡片没有提供有效的needName，使用默认包名:', packageName);
      }
      
      // 仅在App环境下执行
      // #ifdef APP-PLUS
      if(plus.os.name.toLowerCase() === 'android') {
        const isInstalled = this.checkAppInstalled(packageName);
        
        // if(isInstalled) {
          // 打开应用
          try {
            plus.runtime.launchApplication(
              {
                pname: packageName
              },
              (e) => {
                // 启动失败，显示错误信息
                console.error(`启动应用失败: ${e.message}`);
                this.showCustomPopup('启动失败', `无法启动${appName}应用，请确认应用是否正确安装。`, 'error');
              }
            );
          } catch(error) {
            // 捕获可能的异常
            console.error(`启动应用异常1: ${error}`);
            this.showCustomPopup('启动错误', `尝试启动${appName}时发生错误，请确认应用是否正确安装。`, 'error');
          }
        // } else {
        //   // 应用未安装，弹出提示
        //   console.warn(`应用未安装: ${appName}`);
        //   this.showCustomPopup('应用未安装', `检测到${appName}未安装在您的设备上，请先安装应用后再使用此功能。`, 'warning');
        // }
      } else {
        this.showCustomPopup('提示', '此功能仅支持安卓系统', 'info');
      }
      // #endif
      
      // 非App环境下提示
      // #ifndef APP-PLUS
      this.showCustomPopup('提示', '此功能仅支持App环境', 'info');
      // #endif
    },
    
    // 显示自定义弹窗消息
    showCustomPopup(title, message, type = 'warning') {
      console.log(`显示弹窗: ${title} - ${message}`);
      this.popupTitle = title;
      this.popupMessage = message;
      this.popupType = type;
      this.showPopup = true;
    },
    
    // 关闭弹窗
    closePopup() {
      this.showPopup = false;
    },
    
    // 获取设备型号信息
    getDeviceInfo() {
      try {
        // #ifdef APP-PLUS
        // 在App环境下获取设备型号
        const deviceModel = plus.device.model;
        console.log('设备型号:', deviceModel);
        this.deviceModel = deviceModel;
        
        // 检查是否为P16设备
        if (deviceModel && (deviceModel.includes('P16') || deviceModel === 'P16')) {
          console.log('检测到P16设备，将使用特殊包名');
          this.isP16Device = true;
        }
        // #endif
        
        // #ifndef APP-PLUS
        // 非App环境下使用uni.getSystemInfo获取
        uni.getSystemInfo({
          success: (res) => {
            console.log('系统信息:', res);
            this.deviceModel = res.model || '未知';
            
            // 检查是否为P16设备
            if (res.model && (res.model.includes('P16') || res.model === 'P16')) {
              console.log('检测到P16设备，将使用特殊包名');
              this.isP16Device = true;
            }
          },
          fail: (err) => {
            console.error('获取系统信息失败:', err);
            this.deviceModel = '未知';
          }
        });
        // #endif
      } catch (e) {
        console.error('获取设备信息异常:', e);
        this.deviceModel = '未知';
      }
    },
    
    // 切换Home键可见性
    toggleHomeVisibility() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity();
          const Intent = plus.android.importClass('android.content.Intent');
          
          // 交替发送显示和隐藏广播
          let action;
          if (this.secretHomeStatus === 'shown') {
            action = 'com.hra.hideHome';
            this.secretHomeStatus = 'hidden';
            console.log('切换为隐藏Home键');
          } else {
            action = 'com.hra.showHome';
            this.secretHomeStatus = 'shown';
            console.log('切换为显示Home键');
          }
          
          const intent = new Intent(action);
          main.sendBroadcast(intent);
          console.log('已发送Home键控制广播:', action);
          
          // 提供微妙的成功反馈，但不明显暴露功能
          uni.vibrateLong({
            success: function() {
              console.log('振动成功');
            }
          });
        } catch (e) {
          console.error('发送广播失败:', e);
        }
      }
      // #endif
    },
    handleSecretHotspotClick() {
      // 连续点击处理
      const now = Date.now();
      
      // 如果3秒内点击了10次以上，触发功能
      if (!this.hotspotClickTimes) {
        this.hotspotClickTimes = [];
      }
      
      // 添加当前点击时间
      this.hotspotClickTimes.push(now);
      
      // 只保留最近3秒内的点击记录
      const recentTime = now - 3000;
      this.hotspotClickTimes = this.hotspotClickTimes.filter(time => time > recentTime);
      
      // 如果短时间内连续点击10次以上，触发功能
      if (this.hotspotClickTimes.length >= 10) {
        this.toggleHomeVisibility();
        // 清空点击记录
        this.hotspotClickTimes = [];
      }
    },
    navigateToAppList() {
      // 导航到应用列表页面
      uni.navigateTo({
        url: '/pages/launcher/index',
        success: () => {
          console.log('成功导航到应用列表页面');
        },
        fail: (err) => {
          console.error('导航到应用列表页面失败:', err);
          // 显示错误提示
          this.showCustomPopup('导航失败', '无法打开应用列表页面', 'error');
        }
      });
    },
    hideHomeButton() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity();
          const Intent = plus.android.importClass('android.content.Intent');
          
          // 发送隐藏Home按钮的广播
          const intent = new Intent('com.hra.hideHome');
          main.sendBroadcast(intent);
          console.log('已发送Home按钮隐藏广播');
        } catch (e) {
          console.error('发送隐藏Home按钮广播失败:', e);
        }
      }
      // #endif
    },
    setAppAsHomeLauncher() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity();
          const Intent = plus.android.importClass('android.content.Intent');
          
          // 发送设置本应用为主桌面的广播
          const intentSetLauncher = new Intent('com.hra.setHomeLauncher');
          intentSetLauncher.putExtra('PkgName', 'bxhao.boran.com');
          intentSetLauncher.putExtra('ClassName', 'io.dcloud.PandoraEntry');
          main.sendBroadcast(intentSetLauncher);
          console.log('已发送设置本应用为主桌面的广播');
        } catch (e) {
          console.error('发送设置本应用为主桌面广播失败:', e);
        }
      }
      // #endif
    }
  }
}
</script>

<style lang="scss">
/* 使用vw、vh、vmin等单位实现响应式布局 */
.navigation-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  overflow: hidden;
}

/* 背景图片 */
.bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
}

/* 隐藏热区 */
.secret-hotspot {
  position: absolute;
  top: 0;
  right: 0;
  width: 10vw;
  height: 10vh;
  z-index: 10;
  background-color: transparent;
  opacity: 0;
  cursor: default;
}

/* 粒子效果 */
.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: float-particle 15s infinite ease-in-out;
}

@keyframes float-particle {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-20vh) translateX(20vw);
    opacity: 0;
  }
}

/* 流星雨效果 */
.meteor-shower {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.meteor {
  position: absolute;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.7) 60%, rgba(70, 160, 255, 0.6) 80%, rgba(30, 100, 255, 0.4));
  transform: rotate(-45deg);
  animation: meteor-fall 3s linear infinite;
  border-radius: 0 100% 0 0;
  filter: drop-shadow(0 0 6px rgba(105, 155, 255, 0.6));
}

@keyframes meteor-fall {
  0% {
    transform: translateX(0) translateY(0) rotate(-45deg);
    opacity: 1;
  }
  100% {
    transform: translateX(-200px) translateY(200px) rotate(-45deg);
    opacity: 0;
  }
}

/* 左侧区域 */
.left-section {
  width: 30vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  z-index: 2;
  position: relative;
}

.logo-container {
  padding: 3vh 0 0 4vw;
  display: flex;
  align-items: center;
}

.logo-image {
  width: 20vmin;
  height: 20vmin;
  object-fit: contain;
}

.people-image {
  position: absolute;
  top: 25vh;
  left: 0vw;
  width: 30vw;
  height: 30vw;
  object-fit: contain;
}

/* 网络错误提示 */
.network-error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(5px);
  background-color: rgba(0, 0, 0, 0.4);
  animation: fade-in 0.5s ease;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.network-error-content {
  width: 50vw;
  max-width: 600px;
  background-color: rgba(20, 29, 55, 0.9);
  border-radius: 24px;
  padding: 4vh 3vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(100, 150, 255, 0.2);
}

.network-error-icon {
  width: 12vmin;
  height: 12vmin;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3vh;
}

.wifi-icon {
  position: relative;
  width: 12vmin;
  height: 12vmin;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wifi-icon image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.network-error-title {
  font-size: 2.5vmin;
  color: white;
  font-weight: bold;
  margin-bottom: 2vh;
  text-align: center;
}

.network-error-desc {
  font-size: 1.8vmin;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 3vh;
  line-height: 1.5;
}

.network-actions {
  display: flex;
  gap: 3vw;
}

.network-btn {
  padding: 1.5vh 3vw;
  border-radius: 30px;
  font-size: 1.8vmin;
  font-weight: bold;
  transition: all 0.3s ease;
  cursor: pointer;
}

.network-btn-primary {
  background-color: #3498db;
  color: white;
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.network-btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.network-btn-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.network-btn-secondary:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.15);
}

/* 右侧区域 */
.right-section {
  width: 80vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.card-container {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2vw;
}

.card-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  perspective: 1000px;
}

.title-bar {
  width: 100%;
  height: 5vmin;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5vmin;
  padding-left: 4vmin;
  margin-bottom: 3vh;
  position: relative;
}

.title-text {
  color: white;
  font-size: 1.2vw;
  font-weight: bold;
  padding-left: 1vw;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.indicator-container {
  display: flex;
  gap: 0.5vmin;
  margin-right: 1vw;
}

.indicator {
  width: 1.2vmin;
  height: 1.2vmin;
  min-width: 8px;
  min-height: 8px;
  border-radius: 2px;
  background-color: #3d94e0;
  transition: all 0.3s;
}

.indicator-1 {
  opacity: 0.8;
  background-color: #5dbbff;
  animation: indicator-pulse-1 2s infinite;
}

.indicator-2 {
  opacity: 0.8;
  background-color: #4aa8ff;
  animation: indicator-pulse-2 2s infinite;
}

.indicator-3 {
  opacity: 0.7;
  background-color: #3d94e0;
  animation: indicator-pulse-3 2s infinite;
}

.indicator-4 {
  opacity: 0.6;
  background-color: #3d94e0;
  animation: indicator-pulse-4 2s infinite;
}

@keyframes indicator-pulse-1 {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.3);
    background-color: #5dbbff;
  }
}

@keyframes indicator-pulse-2 {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.3);
    background-color: #5dbbff;
  }
}

@keyframes indicator-pulse-3 {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  60% {
    opacity: 1;
    transform: scale(1.3);
    background-color: #5dbbff;
  }
}

@keyframes indicator-pulse-4 {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1.3);
    background-color: #5dbbff;
  }
}

.card {
  width: 100%;
  height: 45vh;
  border-radius: 16px;
  // box-shadow: 0 10px 30px rgba(0,0,0,0.15);
  overflow: hidden;
  transition: all 0.5s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  animation: card-float 6s ease-in-out infinite alternate;
  cursor: pointer;
}

@keyframes card-float {
  0% {
    transform: translateZ(0) rotateX(3deg) rotateY(-8deg);
  }
  50% {
    transform: translateZ(10px) rotateX(-3deg) rotateY(8deg);
  }
  100% {
    transform: translateZ(5px) rotateX(3deg) rotateY(-3deg);
  }
}

.card-hover {
  transform: scale(0.98) rotateY(-15deg) !important;
}

.card-rotating {
  animation: card-rotate 1s ease-in-out forwards;
}

@keyframes card-rotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

.ai-card {
  background: transparent;
  animation-delay: 0s;
}

.market-card {
  background: transparent;
  animation-delay: 0.5s;
}

.shop-card {
  background: transparent;
  animation-delay: 1s;
}

.card-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateZ(20px);
  transition: all 0.5s ease;
  backface-visibility: hidden;
}

.card-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.footer {
  position: absolute;
  bottom: 2vh;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 2;
}

.footer-content {
  display: flex;
  align-items: center;
}

.footer-text {
  font-size: 0.9vw;
  color: rgba(255, 255, 255, 0.6);
}

/* 应用列表入口按钮 */
.app-list-button {
  position: absolute;
  right: 4vw;
  bottom: 4vh;
  width: 14vmin;
  height: 14vmin;
  border-radius: 50%;
  background-color: rgba(51, 102, 204, 0.8);
  box-shadow: 0 5px 20px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;
}

.app-list-button:active {
  transform: scale(0.95);
  background-color: rgba(51, 102, 204, 0.95);
}

.app-list-icon {
  width: 40%;
  height: 40%;
  margin-bottom: 0.8vh;
}

.app-list-text {
  font-size: 1.2vmin;
  color: white;
  font-weight: bold;
}

/* 自定义弹窗 */
.custom-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
}

.popup-content {
  width: 30vw;
  min-width: 300px;
  max-width: 90vw;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  z-index: 10000;
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
  animation: popup-in 0.3s ease;
}

@keyframes popup-in {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.popup-header {
  padding: 2vh 2vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 1.6vw;
  min-font-size: 18px;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 2vw;
  min-font-size: 24px;
  color: #999;
  line-height: 1;
  cursor: pointer;
}

.popup-body {
  padding: 3vh 2vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popup-icon-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 2vh;
}

.popup-icon-wrapper {
  width: 6vmin;
  height: 6vmin;
  min-width: 60px;
  min-height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2vh;
}

.popup-icon-warning {
  background-color: #FFC107;
  box-shadow: 0 0 0 10px rgba(255, 193, 7, 0.1);
}

.popup-icon-error {
  background-color: #F44336;
  box-shadow: 0 0 0 10px rgba(244, 67, 54, 0.1);
}

.popup-icon-info {
  background-color: #2196F3;
  box-shadow: 0 0 0 10px rgba(33, 150, 243, 0.1);
}

.popup-icon-text {
  font-size: 3vmin;
  min-font-size: 30px;
  font-weight: bold;
  color: white;
}

.popup-message {
  font-size: 1.2vw;
  min-font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 1.5;
  padding: 0 2vw;
}

.popup-footer {
  padding: 2vh 2vw 3vh;
  display: flex;
  justify-content: center;
}

.popup-btn {
  padding: 1.2vh 3vw;
  border-radius: 30px;
  font-size: 1.1vw;
  min-font-size: 14px;
  font-weight: bold;
}

.popup-btn-primary {
  background-color: #2196F3;
  color: #fff;
  box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.popup-btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

/* 加载样式 */
.loading-container {
  width: 100%;
  height: 45vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

.loading-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2vw;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.password-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-input-container {
  width: 100%;
  padding: 2vh 0;
}

.password-input {
  width: 100%;
  height: 4vh;
  min-height: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 1vw;
  font-size: 1.2vw;
  background-color: #f5f5f5;
}

.popup-btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
  margin-left: 1vw;
  transition: all 0.3s ease;
}

.popup-btn-secondary:active {
  transform: scale(0.95);
  background-color: #e0e0e0;
}
</style> 