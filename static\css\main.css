
	uni-checkbox .uni-checkbox-input.uni-checkbox-input-disabled{
			background-color: #fff !important;
	}
	.borderB{
		border-bottom: 1upx solid rgba(0, 0, 0, 0.06);
	}
	image {
		position: relative;
	}

	image::after {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		display: block;
		left: 0;
		top: 0;
		bottom: 0;
		right: 0;
		z-index: 2;
	}

	page {
		background-color: #F5F5F5;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		color: #303133;
	}

	uni-page-body,
	#app {
		height: 100%;
	}

	text,
	view,
	input,
	ul,
	li,
	a,
	rich-text,
	picker,
	navigator,
	scroll-view,
	tr,
	td,
	table,
	th,
	swiper {
		box-sizing: border-box;
		margin: 0;
		padding: 0;
		list-style: none;
		text-decoration: none;
	}
.uni-view {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
	page {
		height: 100%;
	}

	.com-color {
		color: rgb(51, 51, 51);
	}

	.com-color-change {
		color: #8a8a8a;
	}

	.com-color-orange {
		color: rgb(255, 0, 0);
	}

	.com-weight {
		font-weight: 700;
	}

	.com-fff {
		color: #fff;
	}

	.com-color-s {
		color: rgb(31, 203, 127);
	}

	.com-color-163 {
		color: rgb(163, 163, 163);
	}

	.com-color-blue {
		color: #0076ff;
	}

	.com-color-120 {
		color: rgb(120, 120, 120);
	}

	.com-fontsize-26 {
		font-size: 26upx;
	}
.com-fontsize-24 {
		font-size: 24upx;
	}
	.com-fontsize-30 {
		font-size: 30upx;
	}
.com-fontsize-34 {
		font-size: 34upx;
	}
	.com-fontsize-36 {
		font-size: 36upx;
	}

	.com-fontsize-32 {
		font-size: 32upx;
	}

	.com-line {
		border-bottom: 1upx solid rgba(0, 0, 0, 0.06);
	}

	page,
	view,
	input,
	image,
	text,
	textarea,
	scroll-view,
	cover-image,
	cover-view,
	movable-area,
	movable-view,
	swiper,
	swiper-item,
	icon,
	button,
	checkbox,
	checkbox-gropu,
	editor,
	form,
	input,
	label,
	picker,
	picker-view,
	picker-view-column,
	radio,
	radio-group,
	switch,
	navigator,
	camera,
	video,
	map,
	tr,
	td,
	table,
	th,
	canvas {
		box-sizing: border-box;
		margin: 0upx;
		padding: 0upx;
		/* font-size: 28upx; */
		font-family: PingFang-SC-Regular;
	}

	.text-overflow {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.text-line-overflow {
		display: -webkit-box;
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}
	.text-line-overflow-three {
		display: -webkit-box;
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
	}
	.page-box {
		width: 100vw;
		min-height: 100vh;
	}

	.com-shadow {
		box-shadow: 0px 0px 10px #ababab;
	}
	
	.com-shadow_1 {
		box-shadow: 0px 0px 10px #a2a2a2;
	}
	
	.com-back-blue {
		background: linear-gradient(to right, #5ba9ff, #2772ff)
	}

	.com-back-red {
		background: linear-gradient(to right, #f58674, #f46e59)
	}

	.com-back-green {
		background: linear-gradient(to right, #1fcb7f, #43d594)
	}

	.com-back-g {
		background-color: rgb(229, 229, 229);

	}

	.dis-ali {
		display: flex;
		align-items: center;
	}
	.flex-column{
		flex-direction: column;
	}
	.jc_bet{
		justify-content: space-between;
	}
	.jc_cen{
		justify-content: center;
	}
	.font-bold {
		font-weight: 900;
	}

