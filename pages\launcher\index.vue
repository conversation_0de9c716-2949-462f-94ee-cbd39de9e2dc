<template>
  <view class="app-launcher-container">
    <!-- 背景图片 -->
    <image class="bg-image" src="/static/<EMAIL>" mode="aspectFill"></image>
    
    <!-- 粒子效果和流星雨 -->
    <view class="particles-container">
      <view class="particle" v-for="(item, index) in particles" :key="`p-${index}`"
        :style="{ left: item.left, top: item.top, width: item.size, height: item.size, 
                 animationDelay: item.delay, opacity: item.opacity }">
      </view>
    </view>
    
    <view class="meteor-shower">
      <view class="meteor" v-for="(item, index) in meteors" :key="`m-${index}`"
        :style="{ left: item.left, top: item.top, width: item.width, height: item.height, 
                 animationDelay: item.delay, animationDuration: item.duration }">
      </view>
    </view>
    
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-left">
        <image class="logo-image" src="/static/<EMAIL>" mode="aspectFit"></image>
        <text class="header-title">应用列表</text>
      </view>
      <view class="header-right">
        <view class="back-button" @click="goBack">
          <text class="back-text">返回</text>
        </view>
      </view>
    </view>
    
    <!-- 应用列表 -->
    <view class="app-list-container">
      <view class="search-box">
        <input class="search-input" v-model="searchKeyword" placeholder="搜索应用" />
        <view class="search-icon">
          <text class="search-icon-text">🔍</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在扫描应用列表...</text>
      </view>
      
      <!-- 应用列表网格 -->
      <scroll-view class="app-grid" scroll-y="true" v-else>
        <view class="empty-hint" v-if="filteredApps.length === 0">
          <text class="empty-text">未找到应用</text>
        </view>
        <view class="app-grid-content">
          <view class="app-item" v-for="(app, index) in filteredApps" :key="index" @click="launchApp(app)" hover-class="app-item-hover">
            <view class="app-icon">
              <view class="default-app-icon" v-if="!app.iconUrl">
                <text class="default-icon-text">{{getAppInitial(app.appName)}}</text>
              </view>
              <img v-else :src="app.iconUrl" mode="aspectFit" class="app-icon-image" @error="handleIconError(app)"></img>
            </view>
            <text class="app-name">{{ app.appName }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <view class="footer-content">
        <text class="footer-text">© 2025 山东普古物联网科技有限公司 v{{appVersion}} [{{deviceModel}}]</text>
        <text class="cache-info" v-if="lastUpdateTime > 0">更新时间: {{new Date(lastUpdateTime).toLocaleTimeString()}}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      appVersion: '1.1.3',
      deviceModel: '',
      // 粒子和流星效果
      particles: [],
      meteors: [],
      // 应用列表
      appList: [],
      isLoading: true,
      searchKeyword: '',
      // 系统信息
      isAndroid: false,
      // 缓存控制
      isCacheLoaded: false,
      isBackgroundUpdating: false,
      lastUpdateTime: 0
    }
  },
  computed: {
    // 过滤后的应用列表
    filteredApps() {
      if (!this.searchKeyword) {
        return this.appList;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.appList.filter(app => {
        return app.appName.toLowerCase().includes(keyword);
      });
    }
  },
  onLoad() {
    // 生成随机粒子
    this.generateParticles();
    // 生成流星雨
    this.generateMeteors();
    // 获取设备信息
    this.getDeviceInfo();
    // 获取应用列表 - 先加载缓存
    this.loadAppsFromCache();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    
    // 生成粒子效果
    generateParticles() {
      const particles = [];
      
      for (let i = 0; i < 50; i++) {
        particles.push({
          left: Math.random() * 100 + 'vw',
          top: Math.random() * 100 + 'vh',
          size: (Math.random() * 6 + 1) + 'px',
          delay: (Math.random() * 5) + 's',
          opacity: Math.random() * 0.5 + 0.1
        });
      }
      
      this.particles = particles;
    },
    
    // 生成流星雨
    generateMeteors() {
      const meteors = [];
      
      for (let i = 0; i < 8; i++) {
        meteors.push({
          left: (50 + Math.random() * 40) + 'vw',
          top: Math.random() * 20 + 'vh',
          width: (Math.random() * 200 + 100) + 'px',
          height: '1px',
          delay: (Math.random() * 15) + 's',
          duration: (Math.random() * 3 + 1) + 's'
        });
      }
      
      this.meteors = meteors;
    },
    
    // 从缓存加载应用列表
    loadAppsFromCache() {
      this.isLoading = true;
      
      try {
        const cachedApps = uni.getStorageSync('app_list_cache');
        const cacheTime = uni.getStorageSync('app_list_cache_time') || 0;
        this.lastUpdateTime = cacheTime;
        
        if (cachedApps && cachedApps.length > 0) {
          console.log('从缓存加载应用列表，缓存时间:', new Date(cacheTime).toLocaleString());
          this.appList = cachedApps;
          this.isLoading = false;
          this.isCacheLoaded = true;
          console.log(this.appList)
          // 检查缓存是否超过1小时，如果超过则在后台更新
          const ONE_HOUR = 60 * 60 * 1000;
          if (Date.now() - cacheTime > ONE_HOUR) {
            console.log('缓存已过期，在后台更新应用列表');
            this.updateAppsInBackground();
          } else {
            // 如果缓存不超过1小时，延迟5秒后在后台更新
            setTimeout(() => {
              this.updateAppsInBackground();
            }, 5000);
          }
        } else {
          console.log('未找到缓存或缓存为空，获取应用列表');
          this.getInstalledApps();
        }
      } catch (e) {
        console.error('读取缓存异常:', e);
        this.getInstalledApps();
      }
    },
    
    // 后台更新应用列表
    updateAppsInBackground() {
      // 避免重复执行后台更新
      if (this.isBackgroundUpdating) {
        return;
      }
      
      this.isBackgroundUpdating = true;
      console.log('开始在后台更新应用列表');
      
      this.getAppListWithIcons().then(apps => {
        console.log('后台更新应用列表成功，数量:', apps.length);
        
        // 按应用名称排序
        if (apps.length > 0) {
          apps.sort((a, b) => {
            return a.appName.localeCompare(b.appName, 'zh');
          });
        }
        
        // 更新缓存
        const now = Date.now();
        uni.setStorageSync('app_list_cache', apps);
        uni.setStorageSync('app_list_cache_time', now);
        this.lastUpdateTime = now;
        
        // 只有当应用数量不同或者缓存加载失败时才更新显示
        if (!this.isCacheLoaded || this.appList.length !== apps.length) {
          console.log('应用列表有变化，更新显示');
          this.appList = apps;
        }
      }).catch(err => {
        console.error('后台更新应用列表失败:', err);
        // 后台更新失败不显示错误提示
      }).finally(() => {
        this.isBackgroundUpdating = false;
      });
    },
    
    // 获取设备信息
    getDeviceInfo() {
      try {
        // #ifdef APP-PLUS
        // 在App环境下获取设备型号
        const deviceModel = plus.device.model;
        console.log('设备型号:', deviceModel);
        this.deviceModel = deviceModel;
        
        if (plus.os.name.toLowerCase() === 'android') {
          this.isAndroid = true;
        }
        // #endif
        
        // #ifndef APP-PLUS
        // 非App环境下使用uni.getSystemInfo获取
        uni.getSystemInfo({
          success: (res) => {
            console.log('系统信息:', res);
            this.deviceModel = res.model || '未知';
            this.isAndroid = res.platform === 'android';
          },
          fail: (err) => {
            console.error('获取系统信息失败:', err);
            this.deviceModel = '未知';
          }
        });
        // #endif
      } catch (e) {
        console.error('获取设备信息异常:', e);
        this.deviceModel = '未知';
      }
    },
    
    // 获取已安装的应用列表
    getInstalledApps() {
      this.isLoading = true;
      
      // 清空应用列表
      this.appList = [];
      
      // 记录开始时间
      const startTime = Date.now();
      
      // 使用新的方法获取应用列表（包括图标）
      this.getAppListWithIcons().then(apps => {
        console.log('获取应用列表成功，数量:', apps.length);
        this.appList = apps;
        
        // 按应用名称排序
        if (this.appList.length > 0) {
          this.appList.sort((a, b) => {
            return a.appName.localeCompare(b.appName, 'zh');
          });
        }
        
        // 保存到缓存
        const now = Date.now();
        uni.setStorageSync('app_list_cache', this.appList);
        uni.setStorageSync('app_list_cache_time', now);
        this.lastUpdateTime = now;
        this.isCacheLoaded = true;
        
      }).catch(err => {
        console.error('获取应用列表失败:', err);
        // 只在非Android环境下使用测试数据
        if (!this.isAndroid) {
          this.addTestApps();
        } else {
          // 在Android环境下出错时，尝试再次获取
          setTimeout(() => {
            this.getAppListWithIcons(true).then(apps => {
              console.log('重试获取应用列表成功，数量:', apps.length);
              this.appList = apps;
              
              // 按应用名称排序
              if (this.appList.length > 0) {
                this.appList.sort((a, b) => {
                  return a.appName.localeCompare(b.appName, 'zh');
                });
              }
              
              // 保存到缓存
              const now = Date.now();
              uni.setStorageSync('app_list_cache', this.appList);
              uni.setStorageSync('app_list_cache_time', now);
              this.lastUpdateTime = now;
              this.isCacheLoaded = true;
              
            }).catch(retryErr => {
              console.error('重试获取应用列表失败:', retryErr);
              uni.showToast({
                title: '无法获取应用列表',
                icon: 'none',
                duration: 2000
              });
            }).finally(() => {
              this.isLoading = false;
            });
          }, 500);
        }
      }).finally(() => {
        // 计算耗时
        const endTime = Date.now();
        console.log('应用列表加载耗时:', (endTime - startTime) + 'ms');
        
        // 取消加载状态
        this.isLoading = false;
      });
    },
    
    // 新方法：同时获取应用和图标
    getAppListWithIcons(isRetry = false) {
      return new Promise((resolve, reject) => {
        // 非APP环境直接返回测试数据
        // #ifndef APP-PLUS
        setTimeout(() => {
          const testApps = this.generateTestApps();
          resolve(testApps);
        }, 500);
        return;
        // #endif
        
        // 非Android环境也返回测试数据
        // #ifdef APP-PLUS
        if (plus.os.name.toLowerCase() !== 'android') {
          setTimeout(() => {
            const testApps = this.generateTestApps();
            resolve(testApps);
          }, 500);
          return;
        }
        
        try {
          console.log('使用优化后的方法获取应用列表...');
          
          // 为每次调用重新获取Android服务和类
          plus.android.importClass('android.graphics.drawable.BitmapDrawable');
          const BitmapFactory = plus.android.importClass("android.graphics.BitmapFactory");
          const Base64 = plus.android.importClass("android.util.Base64");
          const Bitmap = plus.android.importClass('android.graphics.Bitmap');
          const ByteArrayOutputStream = plus.android.importClass("java.io.ByteArrayOutputStream");
          const Canvas = plus.android.importClass('android.graphics.Canvas');
          plus.android.importClass('java.util.ArrayList');
          plus.android.importClass('android.content.pm.PackageInfo');
          plus.android.importClass('android.content.pm.PackageManager');
          const ApplicationInfo = plus.android.importClass('android.content.pm.ApplicationInfo');
          
          // 获取主Activity和包管理器 - 每次都重新获取
          const MainActivity = plus.android.runtimeMainActivity();
          
          if (!MainActivity) {
            throw new Error('无法获取MainActivity');
          }
          
          const PackageManager = MainActivity.getPackageManager();
          
          if (!PackageManager) {
            throw new Error('无法获取PackageManager');
          }
          
          // 获取已安装的包
          const pinfo = PackageManager.getInstalledPackages(0);
          
          if (!pinfo) {
            throw new Error('无法获取已安装的包列表');
          }
          
          console.log('获取到包总数:', pinfo.size());
          
          // 应用列表
          const appList = [];
          
          // 统计应用处理情况的计数器
          const stats = {
            totalPackages: pinfo.size(),
            processed: 0,
            withLaunchIntent: 0,
            filtered: 0,
            added: 0
          };
          
          // 批量处理应用，每次20个
          const processBatch = (startIndex, endIndex) => {
            for (let i = startIndex; i < endIndex && i < pinfo.size(); i++) {
              stats.processed++;
              try {
                // 获取包信息
                const pkginfo = pinfo.get(i);
                if (!pkginfo) continue;
                
                // 检查是否是系统应用
                const appInfoObj = pkginfo.plusGetAttribute("applicationInfo");
                if (!appInfoObj) continue;
                
                const issysapk = ((appInfoObj.plusGetAttribute("flags") & ApplicationInfo.FLAG_SYSTEM) != 0) ? true : false;
                
                // 获取包名
                const packageName = pkginfo.plusGetAttribute("packageName");
                if (!packageName) continue;
                
                // 尝试获取启动Intent
                let launchIntent = null;
                try {
                  launchIntent = PackageManager.getLaunchIntentForPackage(packageName);
                  if (launchIntent) stats.withLaunchIntent++;
                } catch (intentErr) {
                  // 忽略获取Intent失败的错误
                }
                
                // 只处理非系统应用或有启动Intent的系统应用
                const shouldProcess = !issysapk || launchIntent !== null;
                
                if (shouldProcess) {
                  // 获取应用基本信息
                  const appName = appInfoObj.loadLabel(PackageManager).toString();
                  const apkinfo = {
                    appName: appName || packageName,  // 如果无法获取名称，使用包名作为备用
                    packageName: packageName,
                    versionName: pkginfo.plusGetAttribute("versionName") || '',
                    versionCode: pkginfo.plusGetAttribute("versionCode") || 0,
                    iconUrl: '',
                    activityName: '',  // 初始化为空字符串
                    isSystemApp: issysapk
                  };
                  
                  // 安全地获取activityName
                  try {
                    if (launchIntent) {
                      // 检查getComponent方法是否存在
                      if (typeof launchIntent.getComponent === 'function') {
                        const component = launchIntent.getComponent();
                        if (component && typeof component.getClassName === 'function') {
                          apkinfo.activityName = component.getClassName();
                        }
                      } else if (launchIntent.component && typeof launchIntent.component.getClassName === 'function') {
                        // 尝试备用方式获取
                        apkinfo.activityName = launchIntent.component.getClassName();
                      } else {
                        // 无法获取activity名称
                        console.log('无法获取应用活动名称:', packageName);
                      }
                    }
                  } catch (activityError) {
                    console.warn('获取Activity名称失败:', packageName, activityError);
                  }
                  
                  // 获取图标并转为Base64 - 即使失败也保留应用信息
                  try {
                    if (appInfoObj) {
                      // 获取图标
                      const appIcon = appInfoObj.loadIcon(PackageManager);
                      
                      // 处理图标
                      let bimp = null;
                      try {
                        // 尝试直接获取Bitmap
                        bimp = appIcon.getBitmap();
                      } catch(e) {
                        try {
                          // 如果失败，创建新的Bitmap并绘制
                          bimp = Bitmap.createBitmap(
                            appIcon.getIntrinsicWidth(), 
                            appIcon.getIntrinsicHeight(), 
                            Bitmap.Config.ARGB_8888
                          );
                          const canvas = new Canvas(bimp);
                          appIcon.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                          appIcon.draw(canvas);
                        } catch (bitmapErr) {
                          console.warn('创建图标位图失败:', packageName);
                        }
                      }
                      
                      if (bimp) {
                        // 转换为Base64
                        try {
                          const baos = new ByteArrayOutputStream();
                          bimp.compress(Bitmap.CompressFormat.JPEG, 70, baos);
                          baos.flush();
                          
                          const bitmapBytes = baos.toByteArray();
                          const result = "data:image/jpeg;base64," + Base64.encodeToString(bitmapBytes, Base64.DEFAULT);
                          
                          // 保存图标
                          apkinfo.iconUrl = result;
                          
                          // 关闭流和释放资源
                          baos.close();
                          if (bimp) bimp.recycle();
                        } catch (streamErr) {
                          console.warn('图标编码失败:', packageName);
                        }
                      }
                    }
                  } catch (iconError) {
                    console.warn('获取图标失败:', packageName, iconError);
                  }
                  
                  // 无论是否有启动Intent，都添加到应用列表
                  appList.push(apkinfo);
                  stats.added++;
                } else {
                  stats.filtered++;
                }
              } catch (e) {
                console.warn('处理应用信息失败:', i, e);
              }
            }
          };
          
          // 创建临时UI更新函数
          const updateUIWithCurrentApps = () => {
            if (appList.length > 0) {
              // 复制当前应用列表
              const currentApps = [...appList];
              // 按名称排序
              currentApps.sort((a, b) => a.appName.localeCompare(b.appName, 'zh'));
              // 更新UI（仅在需要立即展示时使用）
              if (this.isBackgroundUpdating === false) {
                this.appList = currentApps;
              }
            }
          };
          
          // 是否处于后台更新模式
          const isBackgroundMode = this.isBackgroundUpdating === true;
          
          // 全部处理完成的标志
          let isProcessingComplete = false;
          
          // 修改为从指定位置开始处理，处理到结束，而非批量处理
          if (isBackgroundMode) {
            // 后台更新模式：批量处理
            // 每批次大小
            const BATCH_SIZE = 20;
            let currentIndex = 0;
            
            // 处理第一批
            processBatch(0, BATCH_SIZE);
            currentIndex += BATCH_SIZE;
            
            // 是否处理完成的标志
            let isCompleted = false;
            
            // 如果应用数超过BATCH_SIZE，启动异步继续处理
            if (pinfo.size() > BATCH_SIZE) {
              // 先进行一次UI更新（即使是后台模式也进行临时更新）
              updateUIWithCurrentApps();
              
              const processNextBatch = () => {
                if (currentIndex < pinfo.size()) {
                  const nextBatchEnd = Math.min(currentIndex + BATCH_SIZE, pinfo.size());
                  processBatch(currentIndex, nextBatchEnd);
                  currentIndex = nextBatchEnd;
                  
                  // 打印进度
                  const progress = Math.floor((currentIndex / pinfo.size()) * 100);
                  console.log(`应用列表处理进度: ${progress}%, 已添加: ${stats.added}个`);
                  
                  // 继续处理下一批
                  setTimeout(processNextBatch, 10);
                } else {
                  // 处理完成后输出统计信息
                  console.log('应用列表处理统计:', JSON.stringify(stats));
                  isCompleted = true;
                  isProcessingComplete = true;
                  
                  // 处理完成后再次调用resolve
                  finalizeAndResolve();
                }
              };
              
              // 开始处理后续批次
              setTimeout(processNextBatch, 100);
              
              // 设置超时保护，确保即使出现问题也会返回当前处理的应用
              setTimeout(() => {
                if (!isCompleted) {
                  console.warn('应用处理超时，返回当前已处理的应用:', appList.length);
                  isProcessingComplete = true;
                  finalizeAndResolve();
                }
              }, 15000); // 15秒超时
            } else {
              // 只有一批，直接标记为完成
              isProcessingComplete = true;
            }
          } else {
            // 立即显示模式：一次性处理所有应用而不分批
            for (let i = 0; i < pinfo.size(); i++) {
              processBatch(i, i+1);
              
              // 每处理10个更新一次进度
              if (i % 10 === 0) {
                // 打印进度
                const progress = Math.floor((i / pinfo.size()) * 100);
                console.log(`应用列表处理进度: ${progress}%, 已添加: ${stats.added}个`);
              }
            }
            
            // 全部处理完成
            isProcessingComplete = true;
          }
          
          // 释放资源 - 确保每次调用后都正确释放
          const cleanupResources = () => {
            setTimeout(() => {
              try {
                // 手动进行垃圾回收
                plus.android.autoCollection(true);
              } catch (e) {
                console.warn('资源回收失败:', e);
              }
            }, 500);
          };
          
          // 最终处理并解析
          const finalizeAndResolve = () => {
            // 先清理资源
            cleanupResources();
            
            // 按应用名称排序
            appList.sort((a, b) => a.appName.localeCompare(b.appName, 'zh'));
            
            console.log('成功获取应用列表，数量:', appList.length);
            console.log('应用列表处理统计:', JSON.stringify(stats));
            
            // 如果后台处理未完成但需要立即返回结果
            if (isBackgroundMode && !isProcessingComplete) {
              console.log('后台处理尚未完成，但先返回当前结果:', appList.length);
            }
            
            // 返回结果
            resolve(appList);
          };
          
          // 如果处理已完成，直接解析
          if (isProcessingComplete) {
            finalizeAndResolve();
          } else if (!isBackgroundMode) {
            // 如果不是后台模式但处理未完成(不应该发生)，强制解析
            console.warn('非后台模式但处理未完成，强制解析');
            finalizeAndResolve();
          }
          // 后台模式且未完成的情况下，将通过异步回调解析
          
        } catch (e) {
          console.error('获取应用列表失败:', e);
          
          // 如果已经是重试，或者错误不是由PackageManager造成的，返回错误
          if (isRetry || (e.message && e.message.indexOf('PackageManager') === -1)) {
            reject(e);
          } else {
            // 尝试重新导入和获取资源
            try {
              // 手动进行垃圾回收
              plus.android.autoCollection(true);
              
              // 短暂延迟后重新尝试
              setTimeout(() => {
                this.getAppListWithIcons(true)
                  .then(resolve)
                  .catch(reject);
              }, 500);
            } catch (retryError) {
              reject(retryError);
            }
          }
        }
        // #endif
      });
    },
    
    // 生成测试数据
    generateTestApps() {
      return [
        { packageName: 'com.android.settings', appName: '设置', iconUrl: '', activityName: 'com.android.settings.Settings' },
        { packageName: 'com.android.calendar', appName: '日历', iconUrl: '', activityName: 'com.android.calendar.AllInOneActivity' },
        { packageName: 'com.android.camera', appName: '相机', iconUrl: '', activityName: 'com.android.camera.Camera' },
        { packageName: 'com.android.contacts', appName: '联系人', iconUrl: '', activityName: 'com.android.contacts.activities.PeopleActivity' },
        { packageName: 'com.android.email', appName: '邮件', iconUrl: '', activityName: 'com.android.email.activity.Welcome' },
        { packageName: 'com.android.gallery', appName: '图库', iconUrl: '', activityName: 'com.android.gallery.GalleryActivity' },
        { packageName: 'com.android.music', appName: '音乐', iconUrl: '', activityName: 'com.android.music.MusicBrowserActivity' },
        { packageName: 'com.android.calculator', appName: '计算器', iconUrl: '', activityName: 'com.android.calculator.Calculator' },
        { packageName: 'com.android.browser', appName: '浏览器', iconUrl: '', activityName: 'com.android.browser.BrowserActivity' },
        { packageName: 'com.android.filemanager', appName: '文件管理器', iconUrl: '', activityName: 'com.android.filemanager.FileManagerActivity' },
        { packageName: 'com.android.deskclock', appName: '时钟', iconUrl: '', activityName: 'com.android.deskclock.DeskClock' },
        { packageName: 'com.android.note', appName: '备忘录', iconUrl: '', activityName: 'com.android.note.Notes' },
        { packageName: 'com.android.soundrecorder', appName: '录音机', iconUrl: '', activityName: 'com.android.soundrecorder.SoundRecorder' },
        { packageName: 'com.android.video', appName: '视频播放器', iconUrl: '', activityName: 'com.android.video.VideoPlayerActivity' },
        { packageName: 'com.android.quicksearchbox', appName: '搜索', iconUrl: '', activityName: 'com.android.quicksearchbox.SearchActivity' }
      ];
    },
    
    // 添加测试数据（在非Android环境或获取失败时使用）
    addTestApps() {
      this.appList = this.generateTestApps();
    },
    
    // 启动应用
    launchApp(app) {
      console.log('准备启动应用:', app.appName, app.packageName, '系统应用:', app.isSystemApp);
      
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          // 导入需要的类
          const Intent = plus.android.importClass('android.content.Intent');
          const ComponentName = plus.android.importClass('android.content.ComponentName');
          const main = plus.android.runtimeMainActivity();
          
          // 记录使用了哪种启动方式
          let launchMethod = '';
          
          // 方法1: 使用组件名称启动应用（如果有activityName）
          if (app.activityName && app.activityName.trim() !== '') {
            try {
              // 创建显式Intent
              const launchIntent = new Intent();
              // 设置为显式Intent
              launchIntent.setAction(Intent.ACTION_MAIN);
              launchIntent.addCategory(Intent.CATEGORY_LAUNCHER);
              launchIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
              
              // 设置组件
              const cn = new ComponentName(app.packageName, app.activityName);
              launchIntent.setComponent(cn);
              
              // 执行启动
              main.startActivity(launchIntent);
              console.log('应用启动成功 - 通过组件方式');
              launchMethod = '组件名称方式';
              return;
            } catch (e) {
              console.error('组件启动方式失败:', e);
            }
          }
          
          // 方法2: 使用plus.runtime.launchApplication
          try {
            console.log('尝试使用包名启动应用:', app.packageName);
            plus.runtime.launchApplication(
              { pname: app.packageName },
              function(e) {
                console.log('应用启动成功 - 通过plus.runtime方式');
                launchMethod = 'runtime方式';
              },
              function(e) {
                console.error('plus.runtime包名启动应用失败:', e.message);
                
                // 方法3: 使用Intent.ACTION_MAIN + CATEGORY_LAUNCHER
                try {
                  const launchIntent = main.getPackageManager().getLaunchIntentForPackage(app.packageName);
                  if (launchIntent) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    main.startActivity(launchIntent);
                    console.log('应用启动成功 - 通过Intent方式(getLaunchIntent)');
                    launchMethod = 'getLaunchIntent方式';
                  } else {
                    // 方法4: 尝试使用Intent但不指定组件
                    try {
                      const fallbackIntent = new Intent(Intent.ACTION_MAIN);
                      fallbackIntent.addCategory(Intent.CATEGORY_LAUNCHER);
                      fallbackIntent.setPackage(app.packageName);
                      fallbackIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                      main.startActivity(fallbackIntent);
                      console.log('应用启动成功 - 通过Intent包名方式');
                      launchMethod = 'Intent包名方式';
                    } catch (intentError) {
                      console.error('Intent包名启动失败:', intentError);
                      
                      // 方法5: 最后尝试ACTION_VIEW方式
                      try {
                        const viewIntent = new Intent(Intent.ACTION_VIEW);
                        viewIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        viewIntent.setPackage(app.packageName);
                        main.startActivity(viewIntent);
                        console.log('应用启动成功 - 通过ACTION_VIEW方式');
                        launchMethod = 'ACTION_VIEW方式';
                      } catch (viewError) {
                        console.error('ACTION_VIEW启动失败:', viewError);
                        this.showLaunchError();
                      }
                    }
                  }
                } catch (e) {
                  console.error('获取启动Intent失败:', e);
                  this.showLaunchError();
                }
              }
            );
          } catch (e) {
            console.error('包名启动方式失败:', e);
            this.showLaunchError();
          }
        } catch (e) {
          console.error('启动应用异常:', e);
          this.showLaunchError();
        }
      } else {
        uni.showToast({
          title: '非Android系统，无法启动应用',
          icon: 'none'
        });
      }
      // #endif
      
      // #ifndef APP-PLUS
      uni.showToast({
        title: '当前环境不支持启动应用',
        icon: 'none'
      });
      // #endif
    },
    
    // 显示启动错误提示
    showLaunchError() {
      uni.showToast({
        title: '启动应用失败',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 获取应用初始字母
    getAppInitial(appName) {
      if (!appName || appName.length === 0) {
        return 'A';
      }
      
      // 获取第一个字符
      const firstChar = appName.charAt(0);
      
      // 对于中文，使用转拼音的简化方法，取第一个字的首字母
      if (/[\u4e00-\u9fa5]/.test(firstChar)) {
        // 简化处理，这里根据常用字的拼音首字母映射
        // 实际可以使用完整的汉字转拼音库
        const pinyinMap = {
          '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W',
          '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
          '百': 'B', '千': 'Q', '万': 'W', '亿': 'Y', '设': 'S',
          '系': 'X', '工': 'G', '文': 'W', '开': 'K', '关': 'G',
          '应': 'Y', '用': 'Y', '管': 'G', '理': 'L', '器': 'Q',
          '手': 'S', '机': 'J', '电': 'D', '脑': 'N', '图': 'T',
          '片': 'P', '视': 'S', '频': 'P', '音': 'Y', '乐': 'L',
          '日': 'R', '历': 'L', '时': 'S', '钟': 'Z', '计': 'J',
          '算': 'S', '器': 'Q', '相': 'X', '机': 'J', '浏': 'L',
          '览': 'L', '器': 'Q', '网': 'W', '络': 'L', '邮': 'Y',
          '件': 'J', '信': 'X', '息': 'X', '游': 'Y', '戏': 'X',
          '助': 'Z', '手': 'S', '支': 'Z', '付': 'F', '宝': 'B',
          '微': 'W', '信': 'X', '商': 'S', '店': 'D', '淘': 'T',
          '宝': 'B', '京': 'J', '东': 'D'
        };
        
        return pinyinMap[firstChar] || firstChar.toUpperCase();
      }
      
      // 对于数字，直接返回
      if (/[0-9]/.test(firstChar)) {
        return firstChar;
      }
      
      // 默认返回大写字母
      return firstChar.toUpperCase();
    },
    
    // 处理图标加载错误
    handleIconError(app) {
      console.warn('图标加载失败:', app.packageName);
      // 设置为空字符串以显示默认图标
      this.$set(app, 'iconUrl', '');
    }
  }
}
</script>

<style lang="scss">
/* 整体容器 */
.app-launcher-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 背景图片 */
.bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
}

/* 粒子效果 */
.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: float-particle 15s infinite ease-in-out;
}

@keyframes float-particle {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-20vh) translateX(20vw);
    opacity: 0;
  }
}

/* 流星雨效果 */
.meteor-shower {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.meteor {
  position: absolute;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.7) 60%, rgba(70, 160, 255, 0.6) 80%, rgba(30, 100, 255, 0.4));
  transform: rotate(-45deg);
  animation: meteor-fall 3s linear infinite;
  border-radius: 0 100% 0 0;
  filter: drop-shadow(0 0 6px rgba(105, 155, 255, 0.6));
}

@keyframes meteor-fall {
  0% {
    transform: translateX(0) translateY(0) rotate(-45deg);
    opacity: 1;
  }
  100% {
    transform: translateX(-200px) translateY(200px) rotate(-45deg);
    opacity: 0;
  }
}

/* 头部导航 */
.header {
  width: 100%;
  height: 13vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4vw;
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-image {
  width: 10vmin;
  height: 10vmin;
  object-fit: contain;
  margin-right: 2vw;
}

.header-title {
  font-size: 2.5vmin;
  color: white;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-right {
  display: flex;
  align-items: center;
}

.back-button {
  padding: 1vh 2vw;
  background-color: rgba(51, 102, 204, 0.8);
  border-radius: 30px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.95);
  background-color: rgba(51, 102, 204, 0.95);
}

.back-text {
  color: white;
  font-size: 1.8vmin;
  font-weight: bold;
}

/* 应用列表容器 */
.app-list-container {
  flex: 1;
  width: 100%;
  padding: 2vh 4vw;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

/* 搜索框 */
.search-box {
  width: 100%;
  height: 6vh;
  max-height: 60px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  padding: 0 2vw;
  margin-bottom: 2vh;
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 1.8vmin;
  color: white;
  background-color: transparent;
  border: none;
  padding-right: 4vw;
}

.search-icon {
  position: absolute;
  right: 2vw;
  width: 4vmin;
  height: 4vmin;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon-text {
  font-size: 2.5vmin;
  color: rgba(255, 255, 255, 0.8);
}

/* 应用列表 */
.app-grid {
  flex: 1;
  width: 100%;
  height: calc(100% - 8vh);
}

.app-grid-content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 2vh 0;
}

.app-item {
  width: calc(20% - 2vw);
  margin: 1vh 1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  padding: 2vh 1vw;
  cursor: pointer;
  box-sizing: border-box;
}

.app-item:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.2);
}

.app-item-hover {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.2);
}

.app-icon {
  width: 10vmin;
  height: 10vmin;
  border-radius: 15%;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1vh;
}

.app-icon-image {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.default-app-icon {
  width: 80%;
  height: 80%;
  background: linear-gradient(135deg, #4b6cb7, #182848);
  border-radius: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.default-icon-text {
  font-size: 3vmin;
  color: white;
  font-weight: bold;
}

.app-name {
  font-size: 1.5vmin;
  color: white;
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 0.5vw;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 8vmin;
  height: 8vmin;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 3vh;
}

.loading-text {
  font-size: 1.8vmin;
  color: rgba(255, 255, 255, 0.8);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 空结果提示 */
.empty-hint {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10vh 0;
}

.empty-text {
  font-size: 2vmin;
  color: rgba(255, 255, 255, 0.5);
}

/* 底部信息 */
.footer {
  width: 100%;
  height: 6vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-text {
  font-size: 0.9vw;
  color: rgba(255, 255, 255, 0.6);
}

.cache-info {
  font-size: 0.8vw;
  color: rgba(255, 255, 255, 0.4);
  margin-top: 4px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .app-item {
    width: calc(33.33% - 2vw);
  }
}

@media screen and (max-width: 480px) {
  .app-item {
    width: calc(50% - 2vw);
  }
}
</style> 