<template>
	<view class="index">
		<!-- 顶部搜索栏 -->
		<view class="search-box">
			<u-search
				placeholder="搜索商品"
				v-model="searchKeyword"
				:show-action="false"
				:clearabled="true"
				:animation="false"
				shape="round"
				bg-color="#f5f5f5"
				@click="goSearch"
			></u-search>
		</view>
		
		<!-- 轮播图 -->
		<view class="swiper-box">
			<u-swiper
				:list="bannerList"
				:indicator="true"
				indicatorMode="dot"
				circular
				:autoplay="true"
				radius="10"
				bgColor="transparent"
				@click="clickBanner"
			></u-swiper>
		</view>
		
		<!-- 图标导航 -->
		<view class="nav-box">
			<view class="nav-item" v-for="(item, index) in navList" :key="index" @click="navClick(item)">
				<image class="nav-icon" :src="item.icon" mode="aspectFit"></image>
				<text class="nav-text">{{item.name}}</text>
			</view>
		</view>
		
		<!-- 广告位 -->
		<view class="ad-box">
			<image class="ad-image" src="/static/logo.png" mode="aspectFill"></image>
		</view>
		
		<!-- 商品推荐 -->
		<view class="goods-box">
			<view class="goods-header">
				<text class="goods-title">商品推荐</text>
				<text class="goods-more" @click="moreGoods">更多</text>
			</view>
			<view class="goods-list">
				<view class="goods-item" v-for="(item, index) in goodsList" :key="index" @click="goodsDetail(item)">
					<image class="goods-img" :src="item.image" mode="aspectFill"></image>
					<view class="goods-info">
						<text class="goods-name">{{item.name}}</text>
						<text class="goods-price">¥{{item.price}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				bannerList: [
					{
						image: '/static/logo.png',
						title: '轮播图1'
					},
					{
						image: '/static/logo.png',
						title: '轮播图2'
					},
					{
						image: '/static/logo.png',
						title: '轮播图3'
					}
				],
				navList: [
					{
						icon: '/static/logo.png',
						name: '分类1'
					},
					{
						icon: '/static/logo.png',
						name: '分类2'
					},
					{
						icon: '/static/logo.png',
						name: '分类3'
					},
					{
						icon: '/static/logo.png',
						name: '分类4'
					},
					{
						icon: '/static/logo.png',
						name: '分类5'
					}
				],
				goodsList: [
					{
						image: '/static/logo.png',
						name: '商品名称1',
						price: '99.00'
					},
					{
						image: '/static/logo.png',
						name: '商品名称2',
						price: '199.00'
					},
					{
						image: '/static/logo.png',
						name: '商品名称3',
						price: '299.00'
					},
					{
						image: '/static/logo.png',
						name: '商品名称4',
						price: '399.00'
					}
				]
			}
		},
		onLoad() {
			// 页面加载时获取数据
			this.getHomeData()
		},
		methods: {
			// 获取首页数据
			getHomeData() {
				// 这里可以添加API请求获取首页数据
				console.log('获取首页数据')
			},
			// 点击搜索
			goSearch() {
				console.log('跳转到搜索页面')
			},
			// 点击轮播图
			clickBanner(index) {
				console.log('点击了轮播图', index)
			},
			// 点击导航
			navClick(item) {
				console.log('点击了导航', item)
			},
			// 点击更多商品
			moreGoods() {
				console.log('查看更多商品')
			},
			// 点击商品详情
			goodsDetail(item) {
				console.log('查看商品详情', item)
			}
		}
	}
</script>

<style lang="scss">
.index {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.search-box {
	padding: 20rpx 30rpx;
	background-color: #ffffff;
}

.swiper-box {
	padding: 20rpx 30rpx;
}

.nav-box {
	display: flex;
	justify-content: space-between;
	padding: 30rpx;
	background-color: #ffffff;
	border-radius: 10rpx;
	margin: 20rpx 30rpx;
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 120rpx;
}

.nav-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 10rpx;
}

.nav-text {
	font-size: 24rpx;
	color: #333333;
}

.ad-box {
	margin: 20rpx 30rpx;
	border-radius: 10rpx;
	overflow: hidden;
}

.ad-image {
	width: 100%;
	height: 200rpx;
}

.goods-box {
	background-color: #ffffff;
	margin: 20rpx 30rpx;
	border-radius: 10rpx;
	padding: 20rpx;
}

.goods-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
}

.goods-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.goods-more {
	font-size: 24rpx;
	color: #999999;
}

.goods-list {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.goods-item {
	width: 48%;
	background-color: #ffffff;
	border-radius: 10rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.goods-img {
	width: 100%;
	height: 300rpx;
}

.goods-info {
	padding: 16rpx;
}

.goods-name {
	font-size: 28rpx;
	color: #333333;
	line-height: 40rpx;
	height: 80rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.goods-price {
	font-size: 32rpx;
	color: #ff5500;
	font-weight: bold;
	margin-top: 10rpx;
}
</style>
